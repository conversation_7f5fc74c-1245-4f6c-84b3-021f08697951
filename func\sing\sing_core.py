# 唱歌功能
from func.tools.singleton_mode import singleton
from func.log.default_log import DefaultLog
from func.gobal.data import SingData
from func.obs.obs_init import ObsInit
from func.tools.string_util import StringUtil

from func.tts.tts_core import TTsCore
from func.score.oper_score import OperScore
from func.image.image_core import ImageCore

from func.obs.obs_websocket import ObsWebSocket,VideoStatus,VideoControl
from func.vtuber.emote_oper import EmoteOper
from func.vtuber.action_oper import ActionOper
from func.tts.player import MpvPlay

# 导入本地音乐转换服务
from func.sing.music_convert_service import music_convert_service

# 导入酷狗音乐搜索
from kugou import Kugou_music

import requests
import json
import time
import os
import re
from threading import Thread
import asyncio
from concurrent.futures import ThreadPoolExecutor

@singleton
class SingCore:
    # 设置控制台日志
    log = DefaultLog().getLogger()

    singData = SingData()  # 唱歌数据
    # llmData = LLmData()  # llm数据

    ttsCore = TTsCore()  # 语音核心
    imageCore = ImageCore()  # 图片核心
    emoteOper = EmoteOper()  # 表情
    actionOper = ActionOper()  # 动作
    mpvPlay = MpvPlay()  # 播放器

    operScore = OperScore()  # 积分

    def __init__(self):
        self.obs = ObsInit().get_ws()
        # 初始化酷狗音乐实例
        try:
            self.kugou_music = Kugou_music()
            self.log.info("酷狗音乐模块初始化成功")
        except Exception as e:
            self.log.error(f"酷狗音乐模块初始化失败: {e}")
            self.kugou_music = None

    # 唱歌
    def singTry(self,songname, username):
        try:
            if songname != "":
                self.sing(songname, username)
        except Exception as e:
            print(e)
            self.log.exception(f"【singTry】发生了异常：")
            self.singData.is_singing = 2
            self.singData.is_creating_song = 2

    # 唱歌
    def sing(self, songname, username):
        is_created = 0  # 1.已经生成过 0.没有生成过 2.生成失败
        query = songname  # 查询内容
        font_text = ""
        if query.lower().replace(" ", "") != songname.lower().replace(" ", ""):
            font_text = f'根据"{query}"的信息，'

        # =============== 开始-获取真实歌曲名称 =================
        real_songname = songname
        kugou_filename = None

        # 使用酷狗音乐获取真实歌曲名称和标准化文件名
        if self.kugou_music:
            try:
                kugou_filename, full_song_name = self.kugou_music.get_song_filename(query)
                if kugou_filename and full_song_name:
                    real_songname = full_song_name
                    self.log.info(f"酷狗搜索结果: 查询'{query}' -> 歌曲'{real_songname}' -> 文件名'{kugou_filename}'")
                else:
                    self.log.warning(f"酷狗未找到歌曲: {query}")
                    # 如果酷狗找不到，提示用户
                    outputTxt = f"{font_text}歌库不存在《{query}》这首歌曲哦"
                    self.log.info(outputTxt)
                    self.ttsCore.tts_say(outputTxt)
                    return
            except Exception as e:
                self.log.error(f"酷狗搜索异常: {e}")
                # 如果酷狗搜索异常，使用原始歌名
                real_songname = songname

        # 使用真实歌曲名称作为路径
        song_path = f"./output/{real_songname}/"
        # =============== 结束-获取真实歌曲名称 =================

        # =============== 开始-重复点播判断 =================
        if self.exist_song_queues(self.singData.SongMenuList, real_songname) == True:
            outputTxt = (
                f"回复{username}：{font_text}歌单里已经有歌曲《{real_songname}》，请勿重新点播"
            )
            self.ttsCore.tts_say(outputTxt)
            return
        # =============== 结束-重复点播判断 =================

        # =============== 开始-判断本地是否有歌 =================
        # 检查多种可能的文件格式
        possible_files = []

        # 1. 检查基于酷狗文件名格式的文件（在input目录）。目前只放转换后的，不放原曲
        # if kugou_filename:
        #     input_files = [
        #         f"./input/{kugou_filename}.mp3",
        #         f"./input/{kugou_filename}.flac",
        #         f"./input/{kugou_filename}.wav"
        #     ]
        #     possible_files.extend(input_files)

        # 2. 检查转换后的文件（在output目录）
        speaker = music_convert_service.speaker if music_convert_service else "草神"
        output_files = [
            f"{song_path}/Vocals_{speaker}.wav",
            f"{song_path}/{real_songname}_{speaker}.wav"
        ]
        possible_files.extend(output_files)

        # 检查是否存在任何一个文件
        file_exists = False
        existing_file = None
        for file_path in possible_files:
            if os.path.exists(file_path):
                file_exists = True
                existing_file = file_path
                self.log.info(f"找到存在本地歌曲文件: {file_path}")
                break

        if file_exists:
            self.log.info(f"找到存在本地歌曲: {existing_file}")
            outputTxt = f"回复{username}：{font_text}会唱《{real_songname}》这首歌曲哦"
            self.ttsCore.tts_say(outputTxt)
            is_created = 1
        # =============== 结束-判断本地是否有歌 =================

        # =============== 开始：如果不存在歌曲，生成歌曲 =================
        if is_created == 0:
            # 如果有酷狗文件名但没有转换后的文件，先尝试下载
            if kugou_filename and self.kugou_music:
                try:
                    self.log.info(f"尝试从酷狗下载歌曲: {query}")
                    outputTxt = f"回复{username}：{font_text}正在下载歌曲《{real_songname}》，请耐心等待"
                    self.ttsCore.tts_say(outputTxt)

                    # 下载歌曲（使用优化的搜索和下载方法）
                    downloaded_name, file_path = self.kugou_music.search_and_download_music(query)
                    self.log.info(f"酷狗下载完成: {downloaded_name} -> {file_path}")

                    # 检查下载的文件是否存在
                    if os.path.exists(file_path):
                        self.log.info(f"歌曲下载成功，开始转换: {file_path}")
                    else:# 继续进行转换流程
                        self.log.error(f"下载的文件不存在: {file_path}")

                except Exception as e:
                    self.log.error(f"酷狗下载失败: {e}")

            # 播报学习歌曲
            self.log.info(f"歌曲不存在，需要生成歌曲《{real_songname}》")
            outputTxt = f"回复{username}：{font_text}需要学唱歌曲《{real_songname}》，请耐心等待"
            self.ttsCore.tts_say(outputTxt)

            # 其他歌曲在生成的时候等待
            while self.singData.is_creating_song == 1:
                time.sleep(1)
            # 调用Ai学唱歌服务：生成歌曲
            is_created = self.create_song(real_songname, query, song_path, is_created)
        if is_created == 2:
            self.log.info(f"生成歌曲失败《{real_songname}》")
            return
        self.obs.show_text("状态提示", f"已经学会歌曲《{real_songname}》")
        # =============== 结束：如果不存在歌曲，生成歌曲 =================

        # 等待播放
        self.log.info(f"等待播放{username}点播的歌曲《{real_songname}》：{self.singData.is_singing}")
        # 加入播放歌单
        self.singData.SongMenuList.put({"username": username, "songname": real_songname, "is_created": is_created, "song_path": song_path,
                          "query": query, "kugou_filename": kugou_filename})

    # 播放歌曲清单
    def check_playSongMenuList(self):
        if not self.singData.SongMenuList.empty() and self.singData.is_singing == 2:
            # 播放歌曲
            self.singData.play_song_lock.acquire()
            mlist = self.singData.SongMenuList.get()  # 取出歌单播放
            self.singData.SongNowName = mlist  # 赋值当前歌曲名称
            self.singData.is_singing = 1  # 开始唱歌
            # =============== 开始：播放歌曲 =================
            self.obs.control_video("背景音乐", VideoControl.PAUSE.value)
            self.play_song(mlist["is_created"], mlist["songname"], mlist["song_path"], mlist["username"], mlist["query"])
            if self.singData.SongMenuList.qsize() == 0:
                self.obs.control_video("背景音乐", VideoControl.PLAY.value)
            # =============== 结束：播放歌曲 =================
            self.singData.is_singing = 2  # 完成唱歌
            self.singData.SongNowName = {}  # 当前播放歌单清空
            self.singData.play_song_lock.release()

    # 开始生成歌曲
    def create_song(self, songname, query, song_path, is_created):
        try:
            # =============== 开始生成歌曲 =================
            self.singData.create_song_lock.acquire()
            self.singData.is_creating_song = 1
            status_json = {}
            is_download = False

            # =============== 开始-选择二、学习唱歌任务 =================
            if is_download == False:
                # 直接调用本地音乐转换服务
                try:
                    if music_convert_service.music_module:
                        status, songname_result = music_convert_service.music_module.add_conversion_task(
                            music_info=query,
                            speaker=music_convert_service.speaker
                        )
                        status_json = {"status": status, "songName": songname_result}
                        self.log.info(f"本地音乐转换服务调用成功: {status_json}")
                    else:
                        self.log.error("音乐转换模块未初始化")
                        status_json = {"status": "error", "songName": query}
                except Exception as e:
                    self.log.error(f"调用本地音乐转换服务失败: {e}")
                    # 如果本地服务失败，回退到HTTP请求
                    try:
                        jsonStr = requests.get(url=f"{self.singData.singUrl}/append_song/{query}", timeout=(5, 10))
                        status_json = json.loads(jsonStr.text)
                        self.log.info(f"回退到HTTP请求成功: {status_json}")
                    except Exception as http_e:
                        self.log.error(f"HTTP请求也失败: {http_e}")
                        status_json = {"status": "error", "songName": query}
            # =============== 结束-学习唱歌任务 =================

            status = status_json["status"]  # status: "processing" "processed" "waiting" "error"
            songname = status_json["songName"]
            self.log.info(f"准备生成歌曲内容：{status_json}")

            # 如果状态是错误，直接返回失败
            if status == "error":
                self.log.error(f"歌曲《{songname}》转换任务创建失败")
                is_created = 2
            elif status == "processing" or status == "processed" or status == "waiting":
                # 启动异步状态检查任务
                self.log.info(f"开始异步检查歌曲《{songname}》生成状态")

                # 创建异步任务来检查歌曲状态
                def run_async_check():
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    try:
                        # 尝试获取酷狗文件名
                        kugou_filename = None
                        if self.kugou_music:
                            try:
                                kugou_filename, _ = self.kugou_music.get_song_filename(query)
                            except:
                                pass
                        return loop.run_until_complete(self.async_check_song_status(songname, query, kugou_filename))
                    finally:
                        loop.close()

                # 在线程池中运行异步任务，避免阻塞主线程
                with ThreadPoolExecutor(max_workers=1) as executor:
                    future = executor.submit(run_async_check)
                    is_created = future.result()  # 等待异步任务完成
            # =============== 结束生成歌曲 =================
        except Exception as e:
            print(e)
            self.log.exception(f"《{songname}》create_song异常：")
            is_created = 2
        finally:
            self.singData.is_creating_song = 2
            self.singData.create_song_lock.release()
        return is_created

    # 播放歌曲 1.成功 2.没有歌曲播放 3.异常
    def play_song(self, is_created, songname, song_path, username, query):
        try:
            # 播放歌曲
            if is_created == 1:
                self.log.info(f"准备唱歌《{songname}》,播放路径:{song_path}")
                # 开始唱歌服装穿戴
                self.emoteOper.emote_ws(1, 0.2, "唱歌")
                # 播报唱歌文字
                self.ttsCore.tts_say(f"回复{username}：我准备唱一首歌《{songname}》")
                # 循环摇摆动作
                auto_swing_thread = Thread(target=self.actionOper.auto_swing)
                auto_swing_thread.start()
                # ============== 播放音乐 ================
                speaker = music_convert_service.speaker if music_convert_service else "草神"

                # 检查并确定要播放的文件
                vocal_file = None
                accompany_file = None

                # 优先检查转换后的文件
                if os.path.exists(song_path + f"{songname}_{speaker}.wav"):
                    # 使用完整的转换后文件
                    vocal_file = song_path + f"{songname}_{speaker}.wav"
                elif os.path.exists(song_path + f"Vocals_{speaker}.wav"):
                    # 使用人声转换文件
                    vocal_file = song_path + f"Vocals_{speaker}.wav"
                elif os.path.exists(song_path + f"vocal_{speaker}.wav"):
                    # 兼容旧格式
                    vocal_file = song_path + f"vocal_{speaker}.wav"

                # 检查伴奏文件
                if os.path.exists(song_path + "Chord.wav"):
                    accompany_file = song_path + "Chord.wav"

                # 如果没有转换后的文件，检查是否有酷狗下载的原始文件
                # if not vocal_file:
                #     # 从歌单信息中获取酷狗文件名
                #     kugou_filename = None
                #     if hasattr(self.singData, 'SongNowName') and 'kugou_filename' in self.singData.SongNowName:
                #         kugou_filename = self.singData.SongNowName['kugou_filename']

                #     if kugou_filename:
                #         kugou_files = [
                #             f"./input/{kugou_filename}.mp3",
                #             f"./input/{kugou_filename}.flac",
                #             f"./input/{kugou_filename}.wav"
                #         ]
                #         for kugou_file in kugou_files:
                #             if os.path.exists(kugou_file):
                #                 vocal_file = kugou_file
                #                 self.log.info(f"使用酷狗下载的原始文件播放: {vocal_file}")
                #                 break

                if vocal_file and os.path.exists(vocal_file):
                    # 如果有伴奏文件，启动伴奏播放
                    if accompany_file:
                        accompany_thread = Thread(
                            target=self.sing_play,
                            args=("accompany.exe", accompany_file, 100, "0")
                        )
                        accompany_thread.start()

                    # 调用音乐播放器[人声播放]
                    mpv_play_thread = Thread(
                        target=self.sing_play,
                        args=("song.exe", vocal_file, 100, "0"),
                    )
                    mpv_play_thread.start()
                else:
                    self.log.error(f"找不到可播放的音频文件: {song_path}")
                    return 2
                # ================ end ==================
                # 循环等待唱歌结束标志
                time.sleep(3)
                while self.singData.sing_play_flag == 1:
                    time.sleep(1)
                # 伴奏停止
                self.obs.control_video("伴奏", VideoControl.STOP.value)
                # 结束唱歌穿戴
                self.emoteOper.emote_ws(1, 0.2, "唱歌")
                return 1
            else:
                tip = f"已经跳过歌曲《{songname}》，请稍后再点播"
                self.log.info(tip)
                # 加入回复列表，并且后续合成语音
                self.ttsCore.tts_say(f"回复{username}：{tip}")
                return 2
        except Exception as e:
            print(e)
            self.log.exception(f"《{songname}》play_song异常：")
            return 3

    # 播放唱歌
    def sing_play(self, mpv_name, song_path, volume, start):
        self.singData.sing_play_flag = 1
        self.mpvPlay.mpv_play(mpv_name, song_path, volume, start)
        self.singData.sing_play_flag = 0

    # 唱歌线程
    def check_sing(self):
        if not self.singData.SongQueueList.empty():
            song_json = self.singData.SongQueueList.get()
            self.log.info(f"启动唱歌:{song_json}")
            # 启动唱歌
            sing_thread = Thread(
                target=self.singTry, args=(song_json["prompt"], song_json["username"])
            )
            sing_thread.start()

    # http接口：唱歌接口处理
    def http_sing(self,songname,username):
        self.log.info(f'http唱歌接口处理："{username}"点播歌曲《{songname}》')
        song_json = {"prompt": songname, "username": username}
        self.singData.SongQueueList.put(song_json)
        return

    # http接口：点播歌曲列表
    def http_songlist(self,CallBackForTest):
        jsonstr = []
        if len(self.singData.SongNowName) > 0:
            # 当前歌曲
            username = self.singData.SongNowName["username"]
            songname = self.singData.SongNowName["songname"]
            text = f"'{username}'点播《{songname}》"
            jsonstr.append({"songname": text})
        # 播放歌曲清单
        for i in range(self.singData.SongMenuList.qsize()):
            data = self.singData.SongMenuList.queue[i]
            username = data["username"]
            songname = data["songname"]
            text = f"'{username}'点播《{songname}》"
            jsonstr.append({"songname": text})
        str = '({"status": "成功","content": ' + json.dumps(jsonstr) + "})"
        return str

    def inner_sing(self, query, username):
        try:
            # 获取歌曲名称
            songname = query
            # 检查歌曲是否已经生成
            is_created = self.check_down_song(songname)
            if is_created != 1:
                self.log.info(f"歌曲《{songname}》未生成，重新生成")
                # 创建歌曲
                create_song_thread = Thread(
                    target=self.create_song, args=(songname, username)
                )
                create_song_thread.start()
                return 1
            else:
                self.log.info(f"歌曲《{songname}》已经生成，直接播放")
                # 播放歌曲
                self.play_song(songname, username, is_created)
                return 2
        except Exception as e:
            self.log.exception(f"唱歌内部异常：{e}")
            return 3

    # 唱歌入口处理
    def msg_deal(self, traceid, query, uid, user_name):
        # 唱歌
        text = ["唱一下", "唱一首", "唱歌", "点歌", "点播"]
        is_contain = StringUtil.has_string_reg_list(f"^{text}", query)
        if is_contain is not None:
            num = StringUtil.is_index_contain_string(text, query)
            queryExtract = query[num: len(query)]  # 提取提问语句
            queryExtract = queryExtract.strip()

            # 优化搜索关键词处理
            queryExtract = self._optimize_search_keywords(queryExtract)

            self.log.info(f"[{traceid}]唱歌提示：" + queryExtract)
            if queryExtract == "":
                return True
            song_json = {"traceid": traceid, "prompt": queryExtract, "username": user_name}
            self.singData.SongQueueList.put(song_json)

            # 积分
            print("sing----msg_deal-----oper_score-----", query)
            self.operScore.oper_score(uid, user_name, -2, "", "sing")

            return True
        return False

    def _optimize_search_keywords(self, keywords):
        """
        优化搜索关键词，支持多种格式
        """
        if not keywords:
            return keywords
        # 去除多余的空格和标点
        keywords = keywords.strip()
        # 处理常见的分隔符
        keywords = keywords.replace('：', ':').replace('，', ',').replace('。', '')
        # 处理"的"字
        keywords = keywords.replace('的', '')
        # 处理引号
        keywords = keywords.replace('"', '').replace('"', '').replace('"', '').replace("'", '').replace("'", '').replace("'", '')
        # 处理《》书名号
        keywords = keywords.replace('《', '').replace('》', '')
        # 处理常见的连接词
        keywords = keywords.replace(' by ', '-').replace(' BY ', '-').replace('by', '-').replace('BY', '-')
        # 统一中英文分隔符
        keywords = keywords.replace('—', '-').replace('–', '-')

        return keywords.strip()

    # 判断字符是否存在歌曲此队列
    def exist_song_queues(self, queues, name):
        # 当前歌曲
        if "songname" in self.singData.SongNowName and self.singData.SongNowName["songname"] == name:
            return True
        # 歌单里歌曲
        for i in range(queues.qsize()):
            data = queues.queue[i]
            if data["songname"] == name:
                return True
        return False

    def check_down_song(self, songname, kugou_filename=None):
        """
        检查歌曲是否已经生成
        songname: 真实歌曲名称
        kugou_filename: 酷狗格式的文件名（可选）
        返回: 1-已生成, 0-未生成或处理中, 2-生成失败
        """
        speaker = music_convert_service.speaker if music_convert_service else "草神"

        # 1. 检查酷狗下载的原始文件（在input目录）
        if kugou_filename:
            kugou_files = [
                f"./input/{kugou_filename}.mp3",
                f"./input/{kugou_filename}.flac",
                f"./input/{kugou_filename}.wav"
            ]
            for kugou_file in kugou_files:
                if os.path.exists(kugou_file):
                    self.log.info(f"找到酷狗下载文件: {kugou_file}")
                    # 如果有原始文件但没有转换文件，返回0表示需要转换
                    if not os.path.exists(f"./output/{songname}/{songname}_{speaker}.wav"):
                        return 0
                    else:
                        return 1

        # 2. 检查主要的转换后文件
        if os.path.exists(f"./output/{songname}/{songname}_{speaker}.wav"):
            return 1
        # 3. 检查中间文件，表示正在处理中
        elif os.path.exists(f"./output/{songname}/Vocals_{speaker}.wav"):
            return 1
        # elif os.path.exists(f"./output/{songname}/Vocals.wav"):
        #     return 0  # 还在处理中
        # elif os.path.exists(f"./output/{songname}/Chord.wav"):
        #     return 1
        else:
            return 0

    async def async_check_song_status(self, songname, query, kugou_filename=None):
        """异步检查歌曲生成状态"""
        try:
            i = 0
            while self.singData.is_creating_song == 1:
                # 检查歌曲是否生成成功
                is_created = self.check_down_song(songname, kugou_filename)
                if is_created == 1:
                    self.log.info(f"歌曲《{songname}》生成成功")
                    return 1
                elif is_created == 2:
                    self.log.error(f"歌曲《{songname}》生成失败")
                    return 2

                i += 1
                if i >= self.singData.create_song_timout:
                    self.log.error(f"歌曲《{songname}》生成超时，超过{i}秒")
                    return 2

                # 更新状态显示（非阻塞）。每隔5秒更新一次
                if i % 5 == 0:
                    self.obs.show_text("状态提示", f"当前学唱歌曲《{songname}》第{i}秒")
                    self.log.info(f"生成《{songname}》歌曲第[{i}]秒,生成状态:{is_created}")

                # 异步等待1秒
                await asyncio.sleep(1)

            return 2  # 如果循环被中断，返回失败
        except Exception as e:
            self.log.error(f"异步检查歌曲状态异常: {e}")
            return 2