import torch
from fairseq import checkpoint_utils
import torch.serialization

from vencoder.encoder import SpeechEncoder


class ContentVec256L9(SpeechEncoder):
    def __init__(self, vec_path="sovits4.1\pretrain/checkpoint_best_legacy_500.pt", device=None):
        super().__init__()
        print("load model(s) from {}".format(vec_path))

        # 添加安全的全局对象以支持 fairseq 模型加载
        try:
            from fairseq.data.dictionary import Dictionary
            from fairseq.models.fairseq_model import FairseqModel
            from fairseq.models.transformer import TransformerModel
            from fairseq.models.wav2vec import Wav2VecModel
            from fairseq.models.wav2vec.wav2vec2 import Wav2Vec2Model
            from fairseq.models.hubert import HubertModel
            from fairseq.data.data_utils import collate_tokens
            from fairseq.data.encoders.utils import get_whole_word_mask
            from fairseq.data.audio.audio_utils import get_fbank
            import fairseq.models.wav2vec.wav2vec2_asr
            import fairseq.models.hubert.hubert_asr

            # 添加所有可能需要的 fairseq 类到安全全局列表
            safe_globals = [
                Dictionary, FairseqModel, TransformerModel, Wav2VecModel,
                Wav2Vec2Model, HubertModel, collate_tokens, get_whole_word_mask,
                get_fbank
            ]
            torch.serialization.add_safe_globals(safe_globals)
        except Exception as e:
            print(f"Warning: Could not add safe globals: {e}")
            pass

        # 临时修改 torch.load 的默认行为
        original_torch_load = torch.load
        def patched_torch_load(*args, **kwargs):
            if 'weights_only' not in kwargs:
                kwargs['weights_only'] = False
            return original_torch_load(*args, **kwargs)

        torch.load = patched_torch_load

        try:
            models, saved_cfg, task = checkpoint_utils.load_model_ensemble_and_task(
              [vec_path],
              suffix="",
            )
        finally:
            # 恢复原始的 torch.load
            torch.load = original_torch_load
        self.hidden_dim = 256
        if device is None:
            self.dev = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        else:
            self.dev = torch.device(device)
        self.model = models[0].to(self.dev)
        self.model.eval()

    def encoder(self, wav):
        feats = wav
        if feats.dim() == 2:  # double channels
            feats = feats.mean(-1)
        assert feats.dim() == 1, feats.dim()
        feats = feats.view(1, -1)
        padding_mask = torch.BoolTensor(feats.shape).fill_(False)
        inputs = {
          "source": feats.to(wav.device),
          "padding_mask": padding_mask.to(wav.device),
          "output_layer": 9,  # layer 9
        }
        with torch.no_grad():
            logits = self.model.extract_features(**inputs)
            feats = self.model.final_proj(logits[0])
        return feats.transpose(1, 2)
